import { test, expect } from '@playwright/test';

// Helper function to login
async function loginToPortal(page) {
  await page.goto('/login');
  await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

  const loginButton = page.locator('text=Login with email').or(
    page.locator('button:has-text("Login with email")')
  );

  await expect(loginButton).toBeVisible({ timeout: 10000 });
  await loginButton.click();

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await expect(page).toHaveTitle('Welcome Back!');

  const usernameInput = page.locator('input[name="loginName"]');
  await expect(usernameInput).toBeVisible({ timeout: 10000 });
  await usernameInput.fill('<EMAIL>');

  const nextButton = page.locator('button[type="submit"]');
  await expect(nextButton).toBeVisible({ timeout: 5000 });
  await nextButton.click();

  await page.waitForLoadState('networkidle', { timeout: 10000 });

  const passwordInput = page.locator('input[type="password"]');
  await expect(passwordInput).toBeVisible({ timeout: 10000 });
  await passwordInput.fill('10Apples!');

  const submitButton = page.locator('button[type="submit"]');
  await expect(submitButton).toBeVisible({ timeout: 5000 });
  await submitButton.click();

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await expect(page).not.toHaveURL(/\/login$/);
}

test.describe('Navigation Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set consistent desktop viewport for all navigation tests
    await page.setViewportSize({ width: 1920, height: 1080 });
    await loginToPortal(page);
  });

  test('should navigate through all main sections and verify content loads', async ({ page }) => {
    // Start from home page
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify we're on home page
    await expect(page).toHaveURL('/');
    await expect(page.locator('body')).toBeVisible();

    // Take screenshot of home page
    await page.screenshot({ path: 'test-results/navigation-01-home.png', fullPage: true });

    // Navigate to Payments
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    await expect(paymentsNav).toBeVisible();
    await paymentsNav.click();
    await page.waitForLoadState('networkidle');

    // Verify we're on payments page
    await expect(page).toHaveURL(/\/payments/);
    await expect(page.locator('body')).toBeVisible();

    // Take screenshot of payments page
    await page.screenshot({ path: 'test-results/navigation-02-payments.png', fullPage: true });

    // Navigate to Rewards
    const rewardsNav = page.locator('a:has-text("Rewards")').first();
    await expect(rewardsNav).toBeVisible();
    await rewardsNav.click();
    await page.waitForLoadState('networkidle');

    // Verify we're on rewards page
    await expect(page).toHaveURL(/\/rewards/);
    await expect(page.locator('body')).toBeVisible();

    // Take screenshot of rewards page
    await page.screenshot({ path: 'test-results/navigation-03-rewards.png', fullPage: true });

    // Navigate back to Home
    const homeNav = page.locator('a:has-text("Home")').first();
    await expect(homeNav).toBeVisible();
    await homeNav.click();
    await page.waitForLoadState('networkidle');

    // Verify we're back on home page
    await expect(page).toHaveURL('/');
    await expect(page.locator('body')).toBeVisible();
  });

  test('should switch between tabs on home page and verify content changes', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Locate the tabs
    const recentActivityTab = page.locator('button:has-text("Recent Activity"), [role="tab"]:has-text("Recent Activity")').first();
    const statementsTab = page.locator('button:has-text("Statements"), [role="tab"]:has-text("Statements")').first();

    // Verify both tabs are visible
    await expect(recentActivityTab).toBeVisible();
    await expect(statementsTab).toBeVisible();

    // Start with Recent Activity tab (might be default)
    await recentActivityTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Take screenshot of Recent Activity tab
    await page.screenshot({ path: 'test-results/tabs-01-recent-activity.png', fullPage: true });

    // Verify Recent Activity tab is active/selected
    await expect(recentActivityTab).toBeVisible();

    // Switch to Statements tab
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Take screenshot of Statements tab
    await page.screenshot({ path: 'test-results/tabs-02-statements.png', fullPage: true });

    // Verify Statements tab is now active/selected
    await expect(statementsTab).toBeVisible();

    // Switch back to Recent Activity
    await recentActivityTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Verify we can switch back
    await expect(recentActivityTab).toBeVisible();
  });

  test('should verify navigation state persistence', async ({ page }) => {
    // Navigate to Payments
    await page.goto('/payments');
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/payments/);

    // Verify navigation shows current page (if there's active state styling)
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    await expect(paymentsNav).toBeVisible();

    // Navigate to Rewards
    await page.goto('/rewards');
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/rewards/);

    // Verify navigation shows current page
    const rewardsNav = page.locator('a:has-text("Rewards")').first();
    await expect(rewardsNav).toBeVisible();

    // Navigate back to Home
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL('/');

    // Verify navigation shows current page
    const homeNav = page.locator('a:has-text("Home")').first();
    await expect(homeNav).toBeVisible();
  });

  test('should verify all navigation elements remain functional after page interactions', async ({ page }) => {
    // Start on home page
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Interact with tabs first
    const statementsTab = page.locator('button:has-text("Statements"), [role="tab"]:has-text("Statements")').first();
    if (await statementsTab.isVisible()) {
      await statementsTab.click();
      await page.waitForLoadState('networkidle', { timeout: 5000 });
    }

    // Verify main navigation still works after tab interaction
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    await expect(paymentsNav).toBeVisible();
    await expect(paymentsNav).toBeEnabled();

    await paymentsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/payments/);

    // Verify navigation back to home still works
    const homeNav = page.locator('a:has-text("Home")').first();
    await expect(homeNav).toBeVisible();
    await expect(homeNav).toBeEnabled();

    await homeNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL('/');

    // Verify rewards navigation still works
    const rewardsNav = page.locator('a:has-text("Rewards")').first();
    await expect(rewardsNav).toBeVisible();
    await expect(rewardsNav).toBeEnabled();

    await rewardsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/rewards/);
  });

  test('should verify page content loads completely on each navigation', async ({ page }) => {
    const pages = [
      { name: 'Home', url: '/', nav: 'a:has-text("Home")' },
      { name: 'Payments', url: '/payments', nav: 'a:has-text("Payments")' },
      { name: 'Rewards', url: '/rewards', nav: 'a:has-text("Rewards")' }
    ];

    for (const pageInfo of pages) {
      // Navigate to the page
      const navElement = page.locator(pageInfo.nav).first();
      await expect(navElement).toBeVisible();
      await navElement.click();
      await page.waitForLoadState('networkidle');

      // Verify URL
      await expect(page).toHaveURL(new RegExp(pageInfo.url.replace('/', '\\/')));

      // Verify page has loaded content
      await expect(page.locator('body')).toBeVisible();

      // Verify page title is correct
      await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

      // Verify navigation elements are still present and functional
      await expect(page.locator('a:has-text("Home")').first()).toBeVisible();
      await expect(page.locator('a:has-text("Payments")').first()).toBeVisible();
      await expect(page.locator('a:has-text("Rewards")').first()).toBeVisible();

      console.log(`✓ ${pageInfo.name} page loaded successfully`);
    }
  });
});
