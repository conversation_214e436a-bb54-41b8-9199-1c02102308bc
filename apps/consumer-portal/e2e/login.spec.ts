import { test, expect } from '@playwright/test';

test.describe('Cardholder Portal Login', () => {
  test('should successfully login with email credentials', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');

    // Wait for the page to load and verify we're on the cardholder portal
    await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

    // Click "Login with email" button - try multiple possible selectors
    const loginButton = page.locator('text=Login with email').or(
      page.locator('button:has-text("Login with email")').or(
        page.locator('[data-testid="login-with-email"]').or(
          page.locator('button:has-text("Email")')
        )
      )
    );

    await expect(loginButton).toBeVisible({ timeout: 10000 });
    await loginButton.click();

    // Wait for navigation to Zitadel login page
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    await expect(page).toHaveTitle('Welcome Back!');

    // Step 1: Enter username/email
    const usernameInput = page.locator('input[name="loginName"]').or(
      page.locator('input[id="loginName"]')
    );

    await expect(usernameInput).toBeVisible({ timeout: 10000 });
    await usernameInput.fill('<EMAIL>');

    // Click Next button
    const nextButton = page.locator('button[type="submit"]').or(
      page.locator('button:has-text("Next")')
    );

    await expect(nextButton).toBeVisible({ timeout: 5000 });
    await nextButton.click();

    // Step 2: Wait for password page and enter password
    await page.waitForLoadState('networkidle', { timeout: 10000 });

    const passwordInput = page.locator('input[type="password"]').or(
      page.locator('input[name="password"]')
    );

    await expect(passwordInput).toBeVisible({ timeout: 10000 });
    await passwordInput.fill('10Apples!');

    // Click the final submit button
    const submitButton = page.locator('button[type="submit"]').or(
      page.locator('button:has-text("Sign in")').or(
        page.locator('button:has-text("Login")').or(
          page.locator('button:has-text("Continue")')
        )
      )
    );

    await expect(submitButton).toBeVisible({ timeout: 5000 });
    await submitButton.click();

    // Wait for navigation after login - could be dashboard or another page
    await page.waitForLoadState('networkidle', { timeout: 15000 });

    // Verify successful login by checking we're no longer on the login page
    // and that we have navigated somewhere (URL changed)
    await expect(page).not.toHaveURL(/\/login$/);

    // Additional verification - check that we're logged in
    await expect(page.locator('body')).toBeVisible();
  });

  test('should display login page correctly', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');

    // Verify the page loads correctly with the actual title
    await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

    // Check that some form of login button is visible
    const loginButton = page.locator('text=Login with email').or(
      page.locator('button:has-text("Login with email")').or(
        page.locator('[data-testid="login-with-email"]').or(
          page.locator('button:has-text("Email")')
        )
      )
    );

    await expect(loginButton).toBeVisible({ timeout: 10000 });

    // Verify the page structure
    await expect(page.locator('body')).toBeVisible();
  });
});
